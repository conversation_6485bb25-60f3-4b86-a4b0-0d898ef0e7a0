import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_remix/flutter_remix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../app/theme/colors.dart';
import '../utils/device_utils.dart';

class CustomInputField extends StatefulWidget {
  final String hintText;
  final TextEditingController? controller;
  final String? iconPath;
  final Color? iconColor;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool editable;
  final void Function(String)? onChanged;
  final bool isPassword;
  final int? maxLength;
  final String prefix;
  final bool addPrefix;
  final VoidCallback? onClear;
  final bool isUpdateMode;
  final int? maxLines; // New parameter for max lines
  final double borderRadius; // New parameter for border radius
  final double fontSize; // New parameter for font size

  const CustomInputField({
    super.key,
    required this.hintText,
    this.controller,
    this.iconPath,
    this.iconColor,
    this.textInputAction,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.editable = true,
    this.onChanged,
    this.isPassword = false,
    this.maxLength,
    this.prefix = "+",
    this.addPrefix = false,
    this.onClear,
    this.isUpdateMode = false,
    this.maxLines = 1, // Default to 1 for single-line input
    this.borderRadius = 12.0, // Default border radius
    this.fontSize = 16.0, // Default font size
  });

  @override
  _CustomTextInputState createState() => _CustomTextInputState();
}

class _CustomTextInputState extends State<CustomInputField> {
  bool _isObscured = true;

  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);
    // final focusNode = FocusNode();
    return TextFormField(
      controller: widget.controller,
      cursorColor: AppColors.primaryLightTextColor,
      textInputAction: widget.textInputAction,
      style: TextStyle(
        color: AppColors.primaryLightTextColor,
        fontSize: widget.fontSize,
        fontFamily: "Inter-Regular",
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: TextStyle(
          color: AppColors.primaryLightTextColor,
          fontSize: widget.fontSize,
          fontFamily: "Inter-Regular",
          fontWeight: FontWeight.w400,
        ),
        contentPadding: EdgeInsets.symmetric(
          vertical: widget.maxLines != null && widget.maxLines! > 1
              ? 12
              : 8, // Adjust padding for multi-line
          horizontal: 12,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 2,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: BorderSide(
            color: Colors.grey[300]!,
            width: 2,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 2,
          ),
        ),
        isDense: true,
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!widget.isPassword &&
                widget.controller != null &&
                widget.controller!.text.isNotEmpty &&
                !widget.isUpdateMode)
              IconButton(
                icon: Icon(
                  FlutterRemix.close_circle_fill,
                  color: context.theme.disabledColor,
                  size: widget.fontSize,
                ),
                onPressed: () {
                  setState(() {
                    widget.controller!.clear();
                    if (widget.onChanged != null) {
                      widget.onChanged!('');
                    }
                    if (widget.onClear != null) {
                      widget.onClear!();
                    }
                  });
                },
              ),
            if (widget.isPassword)
              IconButton(
                icon: Icon(
                  _isObscured
                      ? FlutterRemix.eye_off_fill
                      : FlutterRemix.eye_fill,
                  color: context.theme.disabledColor,
                  size: isTablet ? 20 : 20,
                ),
                onPressed: () {
                  setState(() {
                    _isObscured = !_isObscured;
                  });
                },
              ),
          ],
        ),
      ),
      enabled: widget.editable,
      keyboardType: widget.keyboardType ?? TextInputType.text,
      inputFormatters: widget.inputFormatters ?? [],
      validator: widget.validator,
      // onTap: () {
      //   // Ensure the keyboard is shown by requesting focus
      //   focusNode.requestFocus();
      // },
      onChanged: (value) {
        String newValue = value;
        if (widget.addPrefix) {
          if (newValue.isNotEmpty && !newValue.startsWith(widget.prefix)) {
            newValue = "${widget.prefix}$newValue";
          }
        }

        if (widget.controller != null) {
          final newText = newValue;
          widget.controller!.value = widget.controller!.value.copyWith(
            text: newText,
            selection: TextSelection.collapsed(offset: newText.length),
          );
        }

        if (widget.onChanged != null) {
          widget.onChanged!(newValue);
        }

        setState(() {}); // Trigger rebuild to update suffix icon visibility
      },
      textCapitalization: TextCapitalization.words,
      obscureText: widget.isPassword ? _isObscured : false,
      maxLength: widget.maxLength,
      maxLines: widget.maxLines, // Apply maxLines
      buildCounter: (context,
              {required currentLength, required isFocused, maxLength}) =>
          null,
    );
  }
}
