import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import '../../app/theme/colors.dart';
import '../utils/device_utils.dart';
import 'custom_text.dart';

class SimpleDropdown<T> extends StatefulWidget {
  final List<T> items;
  final String hintText;
  final T? selectedValue;
  final String Function(T) displayText;
  final void Function(T?) onChanged;
  final void Function(dynamic)? onIdChanged;
  final String? Function(T?)? validator;
  final dynamic Function(T)? idSelector;
  final double? height;
  final double borderRadius;

  const SimpleDropdown({
    super.key,
    required this.items,
    required this.hintText,
    required this.displayText,
    required this.onChanged,
    this.selectedValue,
    this.onIdChanged,
    this.validator,
    this.idSelector,
    this.height,
    this.borderRadius = 13.0,
  });

  @override
  _SimpleDropdownState<T> createState() => _SimpleDropdownState<T>();
}

class _SimpleDropdownState<T> extends State<SimpleDropdown<T>> {
  @override
  Widget build(BuildContext context) {
    final isTablet = DeviceUtils().isTabletOrIpad(context);

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: SizedBox(
        height: widget.height,
        child: DropdownButtonFormField<T>(
          value: widget.selectedValue,
          decoration: InputDecoration(
            hintText: null, // Remove hintText to use custom hint widget
            isDense: true,
            contentPadding: EdgeInsets.symmetric(
              vertical: 12, // Matches CustomInputField padding
              horizontal: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 2,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: BorderSide(
                color: Colors.grey[300]!,
                width: 2,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
              borderSide: const BorderSide(
                color: AppColors.primaryBorderColor,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Colors.transparent,
          ),
          hint: CustomText(
            text: widget.hintText,
            size: isTablet ? 16 : 16,
            color: AppColors.primaryLightTextColor, // Hint style
            weight: FontWeight.normal,
            fontFamily: 'Inter-Regular',
            underline: false,
          ),
          icon: SvgPicture.asset(
            AssetsManager.droparrow,
            height: isTablet ? 16 : 16,
            width: isTablet ? 16 : 16,
          ),
          menuMaxHeight: isTablet ? 200 : 200,
          items: widget.items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: CustomText(
                text: widget.displayText(item),
                size: isTablet ? 16 : 16,
                weight: FontWeight.w400,
                color: AppColors.primaryLightTextColor,
                underline:
                    widget.selectedValue == item, // Underline selected item
              ),
            );
          }).toList(),
          onChanged: (value) {
            widget.onChanged(value);
            if (widget.idSelector != null && widget.onIdChanged != null) {
              widget.onIdChanged!(widget.idSelector!(value as T));
            }
          },
          validator: widget.validator ??
              (value) {
                if (value == null) {
                  return 'Please select ${widget.hintText.toLowerCase()}';
                }
                return null;
              },
          style: GoogleFonts.inter(
            fontSize: isTablet ? 16 : 16,
            color: AppColors.primaryLightTextColor,
            fontWeight: FontWeight.w400,
            fontStyle: FontStyle.normal,
          ).copyWith(
            fontFamilyFallback: ['Inter-Regular', 'Inter-Regular'],
          ),
        ),
      ),
    );
  }
}
