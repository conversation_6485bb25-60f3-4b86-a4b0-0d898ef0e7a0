import 'dart:io';
import 'package:country_pickers/utils/utils.dart';
import 'package:country_pickers/country.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mastercookai/core/data/models/user_profile_response.dart';
import 'package:mastercookai/core/data/models/user_types_response.dart';
import 'package:mastercookai/core/data/request_query/update_profile_request.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/providers/profile/user_types_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/contact_text_field.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import 'package:mastercookai/core/widgets/image_cropper.dart';
import 'package:mastercookai/core/widgets/simple_dropdown.dart';
import 'package:mastercookai/core/helpers/media_picker_service.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:go_router/go_router.dart';
import '../../../app/theme/colors.dart';

class MyAccountMobileSettings extends ConsumerStatefulWidget {
  const MyAccountMobileSettings({super.key});

  @override
  ConsumerState<MyAccountMobileSettings> createState() =>
      _MyAccountMobileSettingsState();
}

class _MyAccountMobileSettingsState
    extends ConsumerState<MyAccountMobileSettings> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController contactController = TextEditingController();
  final TextEditingController companyNameController = TextEditingController();
  final TextEditingController typesOfUsersController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();

  // State variables
  int selectedGender = 0; // 0 for Male, 1 for Female
  String? selectedUserType;
  int? selectedUserTypeId;
  File? selectedProfileImage;
  String? selectedDay;
  String? selectedMonth;
  String? selectedYear;
  Country? selectedCountry;
  bool _isFieldsPopulated = false;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');

    // Fetch user profile and user types data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
      ref.read(userTypesNotifierProvider.notifier).fetchUserTypes();

      final userProfileState = ref.read(userProfileNotifierProvider);
      if (userProfileState.data?.userProfile != null) {
        _populateFormFields(userProfileState.data!.userProfile!);
      }
    });
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    contactController.dispose();
    companyNameController.dispose();
    typesOfUsersController.dispose();
    dateOfBirthController.dispose();
    super.dispose();
  }

  // Helper method to detect country from phone number
  void _detectCountryFromPhone(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    setState(() {
      if (cleanPhone.startsWith('1') && cleanPhone.length == 11) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      } else if (cleanPhone.startsWith('44')) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('GB');
      } else if (cleanPhone.startsWith('91')) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('IN');
      } else if (cleanPhone.length == 10) {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      } else {
        selectedCountry = CountryPickerUtils.getCountryByIsoCode('US');
      }
    });
  }

  // Helper method to populate form fields with user profile data
  void _populateFormFields(UserProfile userProfile) {
    final nameParts = userProfile.name.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    firstNameController.text = firstName;
    lastNameController.text = lastName;
    emailController.text = userProfile.email;
    contactController.text = userProfile.phone;
    companyNameController.text = userProfile.companyName;
    typesOfUsersController.text = userProfile.userType;
    selectedUserType = userProfile.userType;
    selectedUserTypeId = userProfile.userTypeId;

    // Parse DOB
    final dobComponents = Utils().extractDobComponents(userProfile.dob);
    if (dobComponents != null) {
      setState(() {
        selectedDay = dobComponents['day']?.toString().padLeft(2, '0');
        selectedMonth = dobComponents['month'] as String?;
        selectedYear = dobComponents['year']?.toString();
        dateOfBirthController.text =
            userProfile.dob; // Keep in YYYY-MM-DD format
      });
    }

    _detectCountryFromPhone(userProfile.phone);

    setState(() {
      selectedGender = userProfile.gender.toLowerCase() == 'male' ? 0 : 1;
      _isFieldsPopulated = true;
    });
  }

  // Method to update profile with personal details and image
  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Validate required fields
      if (firstNameController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your first name', isError: true);
        return;
      }
      if (contactController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your contact number', isError: true);
        return;
      }
      if (dateOfBirthController.text.trim().isEmpty) {
        Utils().showFlushbar(context,
            message: 'Please enter your date of birth', isError: true);
        return;
      }
      if (selectedUserTypeId == null) {
        Utils().showFlushbar(context,
            message: 'Please select a user type', isError: true);
        return;
      }

      // Create update profile request
      final request = UpdateProfileRequest(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim().isNotEmpty
            ? lastNameController.text.trim()
            : null,
        gender: selectedGender == 0 ? 'Male' : 'Female',
        dob: dateOfBirthController.text.trim(),
        countryCode: '+${selectedCountry?.phoneCode ?? '1'}',
        contact: contactController.text.trim(),
        companyName: companyNameController.text.trim().isNotEmpty
            ? companyNameController.text.trim()
            : null,
        userTypeId: selectedUserTypeId!,
        profilePic: selectedProfileImage,
      );

      // Call update profile API
      final success = await ref
          .read(userProfileNotifierProvider.notifier)
          .updateProfile(context: context, request: request);

      if (success && context.mounted) {
        // Fetch updated profile data
        await ref.read(userProfileNotifierProvider.notifier).fetchUserProfile();
        // Clear selected image
        setState(() {
          selectedProfileImage = null;
        });
      }
    } catch (e) {
      if (context.mounted) {
        Utils().showFlushbar(context,
            message: 'Failed to update profile: $e', isError: true);
      }
    }
  }

  // Method to pick profile image
  void _pickProfileImage() async {
    final file = await MediaPickerService.pickSingleImage();
    if (file != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageCropper(
            pickedImage: file,
            showCropPresets: true,
            showGridLines: true,
            useDelegate: true,
            enableFreeformCrop: true,
            onImageCropped: (File? croppedImageFile) {
              if (croppedImageFile != null) {
                setState(() {
                  selectedProfileImage = croppedImageFile;
                });
              }
            },
          ),
        ),
      );
    }
  }

  // Helper method to update the date controller when dropdowns change
  void _updateDateController() {
    if (selectedDay != null && selectedMonth != null && selectedYear != null) {
      // Convert short month name to number
      const monthNames = {
        'Jan': '01',
        'Feb': '02',
        'Mar': '03',
        'Apr': '04',
        'May': '05',
        'Jun': '06',
        'Jul': '07',
        'Aug': '08',
        'Sep': '09',
        'Oct': '10',
        'Nov': '11',
        'Dec': '12',
      };
      final monthNumber = monthNames[selectedMonth] ?? '01';
      // Format as YYYY-MM-DD for backend compatibility
      dateOfBirthController.text =
          '$selectedYear-$monthNumber-${selectedDay!.padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);

    // Populate form fields when state changes to success
    if (!_isFieldsPopulated &&
        userProfileState.status == AppStatus.success &&
        userProfileState.data?.userProfile != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFormFields(userProfileState.data!.userProfile!);
      });
    }
    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: CustomAppBar(
        title: 'My Profile',
        onPressed: () => context.pop(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              _buildProfilePictureSection(),
              const SizedBox(height: 24),

              // Personal Details Section
              _buildPersonalDetailsSection(),
              const SizedBox(height: 24),

              // Company Details Section
              _buildCompanyDetailsSection(),
              const SizedBox(height: 32),

              // Save Changes Button
              Center(
                child: CustomButton(
                  text: 'Save Changes',
                  onPressed: _updateProfile,
                  width: double.infinity,
                  height: 50,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  // Profile Picture Section
  Widget _buildProfilePictureSection() {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final userProfile = userProfileState.data?.userProfile;

    return Center(
      child: Column(
        children: [
          // Profile Picture Container with Dashed Border
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.shade400,
                width: 2,
                style: BorderStyle.none, // We'll use DottedBorder instead
              ),
            ),
            child: DottedBorder(
              borderType: BorderType.RRect,
              radius: const Radius.circular(12),
              dashPattern: const [8, 4],
              color: Colors.grey.shade400,
              strokeWidth: 2,
              child: Container(
                width: 116,
                height: 116,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.grey.shade50,
                ),
                child: selectedProfileImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.file(
                          selectedProfileImage!,
                          width: 116,
                          height: 116,
                          fit: BoxFit.cover,
                        ),
                      )
                    : userProfile?.profilePic?.isNotEmpty == true
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image.network(
                              userProfile!.profilePic!,
                              width: 116,
                              height: 116,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return _buildPlaceholderImage();
                              },
                            ),
                          )
                        : _buildPlaceholderImage(),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Upload Profile Picture Button
          Container(
            width: 163,
            height: 30,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: _pickProfileImage,
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const CustomText(
                text: 'Upload Profile Picture',
                color: Colors.white,
                size: 12,
                fontFamily: 'Inter',
                weight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Placeholder image widget
  Widget _buildPlaceholderImage() {
    return Container(
      width: 116,
      height: 116,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.grey.shade50,
      ),
      child: Icon(
        Icons.add,
        size: 40,
        color: Colors.grey.shade400,
      ),
    );
  }

  // Personal Details Section
  Widget _buildPersonalDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Personal Details:',
          size: 15,
          weight: FontWeight.w500,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 10),

        // Gender Selection
        _buildLabeledField('Gender', _buildGenderRadioButtons()),
        const SizedBox(height: 20),

        // First Name
        _buildLabeledField(
          'First Name',
          SizedBox(
            height: 30,
            child: SizedBox(
              height: 25,
              child: CustomInputField(
                hintText: 'Rahul',
                controller: firstNameController,
                validator: Validator.validateFName,
                isUpdateMode: true,
                borderRadius: 6,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Last Name
        _buildLabeledField(
          'Last Name',
          SizedBox(
            height: 30,
            child: SizedBox(
              height: 25,
              child: CustomInputField(
                hintText: 'Verma',
                controller: lastNameController,
                validator: Validator.validateLName,
                isUpdateMode: true,
                borderRadius: 6,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Date of Birth
        _buildLabeledField('Date of birth', _buildDateDropdowns()),
        const SizedBox(height: 20),

        // Email
        _buildLabeledField(
          'Email',
          SizedBox(
            height: 30,
            child: SizedBox(
              height: 25,
              child: CustomInputField(
                hintText: '<EMAIL>',
                controller: emailController,
                validator: Validator.validateEmail,
                editable: false,
                isUpdateMode: true,
                borderRadius: 6,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Contact
        _buildLabeledField(
          'Contact',
          ContactTextField(
            controller: contactController,
            height: 25,
            selectedCountry: selectedCountry!,
            onCountryChanged: (country) {
              setState(() {
                selectedCountry = country;
              });
            },
            validator: Validator.validatePhoneNumber,
            isUpdateMode: true,
            onClear: () {
              contactController.clear();
            },
            borderRadius: 6,
          ),
        ),
      ],
    );
  }

  // Helper method to create labeled fields
  Widget _buildLabeledField(String label, Widget field) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Padding(
            padding: const EdgeInsets.only(top: 5),
            child: CustomText(
              text: label,
              size: 14,
              weight: FontWeight.w400,
              color: AppColors.blackColor,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(child: field),
      ],
    );
  }

  // Gender Radio Buttons
  Widget _buildGenderRadioButtons() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedGender = 0;
              });
            },
            child: Row(
              children: [
                Radio<int>(
                  value: 0,
                  groupValue: selectedGender,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  onChanged: (int? value) {
                    setState(() {
                      selectedGender = value!;
                    });
                  },
                ),
                const SizedBox(
                    width: 4), // Reduced space between radio and text
                CustomText(
                  text: 'Male',
                  size: 14,
                  weight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                selectedGender = 1;
              });
            },
            child: Row(
              children: [
                Radio<int>(
                  value: 1,
                  groupValue: selectedGender,
                  activeColor: AppColors.primaryColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  onChanged: (int? value) {
                    setState(() {
                      selectedGender = value!;
                    });
                  },
                ),
                const SizedBox(
                    width: 4), // Reduced space between radio and text
                CustomText(
                  text: 'Female',
                  size: 14,
                  weight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Date Dropdowns
  Widget _buildDateDropdowns() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(),
        Row(
          children: [
            // Day Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: List.generate(
                    31, (index) => (index + 1).toString().padLeft(2, '0')),
                hintText: '12',
                selectedValue: selectedDay,
                displayText: (day) => day,
                onChanged: (value) {
                  setState(() {
                    selectedDay = value;
                    _updateDateController();
                  });
                },
                validator: (value) => value == null ? 'Select Day' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            // Month Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: [
                  'Jan',
                  'Feb',
                  'Mar',
                  'Apr',
                  'May',
                  'Jun',
                  'Jul',
                  'Aug',
                  'Sep',
                  'Oct',
                  'Nov',
                  'Dec'
                ],
                hintText: 'Mar',
                selectedValue: selectedMonth,
                displayText: (month) => month,
                onChanged: (value) {
                  setState(() {
                    selectedMonth = value;
                    _updateDateController();
                  });
                },
                validator: (value) => value == null ? 'Select Month' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            // Year Dropdown
            Expanded(
              child: SimpleDropdown<String>(
                items: List.generate(
                    100, (index) => (DateTime.now().year - index).toString()),
                hintText: '1982',
                selectedValue: selectedYear,
                displayText: (year) => year,
                onChanged: (value) {
                  setState(() {
                    selectedYear = value;
                    _updateDateController();
                  });
                },
                validator: (value) => value == null ? 'Select Year' : null,
                height: 40,
                borderRadius: 8,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Company Details Section
  Widget _buildCompanyDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Company Details:',
          size: 16,
          weight: FontWeight.w600,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 20),

        // Company Name
        _buildLabeledField(
          'Company Name',
          SizedBox(
            height: 30,
            child: SizedBox(
              height: 25,
              child: CustomInputField(
                hintText: 'Mastercook',
                controller: companyNameController,
                keyboardType: TextInputType.text,
                borderRadius: 6,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Types of Users
        _buildLabeledField('Types of Users', _buildUserTypesDropdownField()),
      ],
    );
  }

  // User types dropdown field
  Widget _buildUserTypesDropdownField() {
    return Consumer(
      builder: (context, ref, child) {
        final userTypesState = ref.watch(userTypesNotifierProvider);
        if (userTypesState.status == AppStatus.success &&
            userTypesState.data != null) {
          final uniqueUserTypes =
              userTypesState.data!.userTypes.toSet().toList();

          return Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<UserType>(
                value: uniqueUserTypes.firstWhere(
                  (type) => type.type == selectedUserType,
                  orElse: () => uniqueUserTypes.isNotEmpty
                      ? uniqueUserTypes.first
                      : UserType(id: 0, type: ''),
                ),
                hint: const Text('Mastercook'),
                isExpanded: true,
                items: uniqueUserTypes.map((userType) {
                  return DropdownMenuItem<UserType>(
                    value: userType,
                    child: Text(userType.type),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedUserType = value.type;
                      selectedUserTypeId = value.id;
                      typesOfUsersController.text = value.type;
                    });
                  }
                },
                icon: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          );
        } else {
          return Container(
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: CustomText(
                text: 'No user types available',
                size: 14,
                weight: FontWeight.w400,
                color: Colors.grey,
              ),
            ),
          );
        }
      },
    );
  }
}
